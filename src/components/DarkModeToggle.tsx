import React from 'react';
import { useDarkMode } from '../contexts/DarkModeContext';

const DarkModeToggle: React.FC = () => {
    const { isDarkMode, toggleDarkMode } = useDarkMode();

    return (
        <button
            onClick={toggleDarkMode}
            className="relative inline-flex items-center justify-center w-12 h-6 bg-secondary-light dark:bg-secondary-dark border border-border-secondary-light dark:border-border-secondary-dark rounded-full transition-all duration-300 hover:bg-secondary-hovered-light dark:hover:bg-secondary-hovered-dark focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-offset-2 focus:ring-offset-background-light dark:focus:ring-offset-background-dark"
            aria-label={
                isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'
            }
        >
            {/* Toggle circle */}
            <span
                className={`absolute w-4 h-4 bg-foreground-icon-light dark:bg-foreground-icon-dark rounded-full transition-transform duration-300 ease-in-out ${
                    isDarkMode ? 'translate-x-3' : '-translate-x-3'
                }`}
            />

            {/* Sun icon (visible in dark mode) */}
            <svg
                className={`absolute w-3 h-3 text-foreground-icon-light transition-opacity duration-300 ${
                    isDarkMode
                        ? 'opacity-100 translate-x-1'
                        : 'opacity-0 -translate-x-1'
                }`}
                fill="currentColor"
                viewBox="0 0 20 20"
            >
                <path
                    fillRule="evenodd"
                    d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                    clipRule="evenodd"
                />
            </svg>

            {/* Moon icon (visible in light mode) */}
            <svg
                className={`absolute w-3 h-3 text-foreground-icon-light transition-opacity duration-300 ${
                    isDarkMode
                        ? 'opacity-0 translate-x-1'
                        : 'opacity-100 -translate-x-1'
                }`}
                fill="currentColor"
                viewBox="0 0 20 20"
            >
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
            </svg>
        </button>
    );
};

export default DarkModeToggle;
