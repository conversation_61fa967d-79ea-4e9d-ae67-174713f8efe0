import React from 'react';

const ThemeShowcase = () => {
    return (
        <div className="space-y-8 p-6">
            {/* Typography Examples */}
            <section className="bg-white rounded-3xl p-8 shadow-soft">
                <h2 className="text-display-md text-secondary-900 mb-6">Typography Scale</h2>
                <div className="space-y-4">
                    <div className="text-display-lg text-primary-600">Display Large</div>
                    <div className="text-display-md text-primary-600">Display Medium</div>
                    <div className="text-display-sm text-primary-600">Display Small</div>
                    <div className="text-6xl text-secondary-800">6XL Heading</div>
                    <div className="text-4xl text-secondary-700">4XL Heading</div>
                    <div className="text-2xl text-secondary-600">2XL Heading</div>
                    <div className="text-xl text-secondary-600">XL Text</div>
                    <div className="text-base text-secondary-600">Base Text (Default)</div>
                    <div className="text-sm text-secondary-500">Small Text</div>
                </div>
            </section>

            {/* Color Palette Examples */}
            <section className="bg-white rounded-3xl p-8 shadow-soft">
                <h2 className="text-display-sm text-secondary-900 mb-6">Color Palettes</h2>
                
                {/* Primary Colors */}
                <div className="mb-8">
                    <h3 className="text-xl font-semibold text-secondary-800 mb-4">Primary Colors</h3>
                    <div className="flex flex-wrap gap-2">
                        <div className="bg-primary-50 w-16 h-16 rounded-xl border border-secondary-200 flex items-center justify-center text-xs">50</div>
                        <div className="bg-primary-100 w-16 h-16 rounded-xl flex items-center justify-center text-xs">100</div>
                        <div className="bg-primary-200 w-16 h-16 rounded-xl flex items-center justify-center text-xs">200</div>
                        <div className="bg-primary-300 w-16 h-16 rounded-xl flex items-center justify-center text-xs">300</div>
                        <div className="bg-primary-400 w-16 h-16 rounded-xl flex items-center justify-center text-xs text-white">400</div>
                        <div className="bg-primary-500 w-16 h-16 rounded-xl flex items-center justify-center text-xs text-white font-bold">500</div>
                        <div className="bg-primary-600 w-16 h-16 rounded-xl flex items-center justify-center text-xs text-white">600</div>
                        <div className="bg-primary-700 w-16 h-16 rounded-xl flex items-center justify-center text-xs text-white">700</div>
                        <div className="bg-primary-800 w-16 h-16 rounded-xl flex items-center justify-center text-xs text-white">800</div>
                        <div className="bg-primary-900 w-16 h-16 rounded-xl flex items-center justify-center text-xs text-white">900</div>
                    </div>
                </div>

                {/* Accent Colors */}
                <div className="mb-8">
                    <h3 className="text-xl font-semibold text-secondary-800 mb-4">Accent Colors</h3>
                    <div className="flex flex-wrap gap-2">
                        <div className="bg-accent-50 w-16 h-16 rounded-xl border border-secondary-200 flex items-center justify-center text-xs">50</div>
                        <div className="bg-accent-100 w-16 h-16 rounded-xl flex items-center justify-center text-xs">100</div>
                        <div className="bg-accent-200 w-16 h-16 rounded-xl flex items-center justify-center text-xs">200</div>
                        <div className="bg-accent-300 w-16 h-16 rounded-xl flex items-center justify-center text-xs">300</div>
                        <div className="bg-accent-400 w-16 h-16 rounded-xl flex items-center justify-center text-xs text-white">400</div>
                        <div className="bg-accent-500 w-16 h-16 rounded-xl flex items-center justify-center text-xs text-white font-bold">500</div>
                        <div className="bg-accent-600 w-16 h-16 rounded-xl flex items-center justify-center text-xs text-white">600</div>
                        <div className="bg-accent-700 w-16 h-16 rounded-xl flex items-center justify-center text-xs text-white">700</div>
                        <div className="bg-accent-800 w-16 h-16 rounded-xl flex items-center justify-center text-xs text-white">800</div>
                        <div className="bg-accent-900 w-16 h-16 rounded-xl flex items-center justify-center text-xs text-white">900</div>
                    </div>
                </div>
            </section>

            {/* Spacing Examples */}
            <section className="bg-white rounded-3xl p-8 shadow-soft">
                <h2 className="text-display-sm text-secondary-900 mb-6">Custom Spacing</h2>
                <div className="space-y-4">
                    <div className="bg-primary-100 h-18 rounded-xl flex items-center px-4">
                        <span className="text-primary-700">Custom spacing: h-18 (4.5rem)</span>
                    </div>
                    <div className="bg-accent-100 w-88 h-16 rounded-xl flex items-center px-4">
                        <span className="text-accent-700">Custom width: w-88 (22rem)</span>
                    </div>
                </div>
            </section>

            {/* Shadow Examples */}
            <section className="bg-secondary-50 rounded-3xl p-8">
                <h2 className="text-display-sm text-secondary-900 mb-6">Custom Shadows</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-white p-6 rounded-2xl shadow-soft">
                        <h3 className="text-lg font-semibold text-secondary-800 mb-2">Soft Shadow</h3>
                        <p className="text-secondary-600">shadow-soft</p>
                    </div>
                    <div className="bg-white p-6 rounded-2xl shadow-medium">
                        <h3 className="text-lg font-semibold text-secondary-800 mb-2">Medium Shadow</h3>
                        <p className="text-secondary-600">shadow-medium</p>
                    </div>
                    <div className="bg-white p-6 rounded-2xl shadow-hard">
                        <h3 className="text-lg font-semibold text-secondary-800 mb-2">Hard Shadow</h3>
                        <p className="text-secondary-600">shadow-hard</p>
                    </div>
                </div>
            </section>

            {/* Animation Examples */}
            <section className="bg-white rounded-3xl p-8 shadow-soft">
                <h2 className="text-display-sm text-secondary-900 mb-6">Custom Animations</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                        <div className="w-16 h-16 bg-primary-500 rounded-xl mx-auto mb-4 animate-fade-in"></div>
                        <p className="text-secondary-600">animate-fade-in</p>
                    </div>
                    <div className="text-center">
                        <div className="w-16 h-16 bg-accent-500 rounded-xl mx-auto mb-4 animate-slide-up"></div>
                        <p className="text-secondary-600">animate-slide-up</p>
                    </div>
                    <div className="text-center">
                        <div className="w-16 h-16 bg-secondary-500 rounded-xl mx-auto mb-4 animate-bounce-gentle"></div>
                        <p className="text-secondary-600">animate-bounce-gentle</p>
                    </div>
                </div>
            </section>

            {/* Button Examples */}
            <section className="bg-white rounded-3xl p-8 shadow-soft">
                <h2 className="text-display-sm text-secondary-900 mb-6">Button Examples</h2>
                <div className="flex flex-wrap gap-4">
                    <button className="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-xl transition-colors font-medium">
                        Primary Button
                    </button>
                    <button className="bg-secondary-500 hover:bg-secondary-600 text-white px-6 py-3 rounded-xl transition-colors font-medium">
                        Secondary Button
                    </button>
                    <button className="bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-xl transition-colors font-medium">
                        Accent Button
                    </button>
                    <button className="border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white px-6 py-3 rounded-xl transition-all font-medium">
                        Outline Button
                    </button>
                </div>
            </section>
        </div>
    );
};

export default ThemeShowcase;
