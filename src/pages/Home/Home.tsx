import React from 'react';
import ErrorBoundary from '@components/ErrorBoundary';
import DarkModeToggle from '@components/DarkModeToggle';

const Home = () => {
    return (
        <div className="bg-background-light dark:bg-background-dark min-h-screen transition-colors">
            <div className="container p-4">
                <ErrorBoundary>
                    {/* Header with dark mode toggle */}
                    <div className="flex justify-between items-center mb-8">
                        <h1 className="text-display-lg text-foreground-title-light dark:text-foreground-title-dark animate-fade-in">
                            Tango Copilot
                        </h1>
                        <DarkModeToggle />
                    </div>

                    {/* Primary card using exact color scheme */}
                    <div className="bg-primary-light dark:bg-primary-dark text-foreground-primary-light dark:text-foreground-primary-dark rounded-3xl p-6 shadow-soft mt-8 hover:bg-primary-hovered-light dark:hover:bg-primary-hovered-dark transition-colors">
                        <h2 className="text-display-sm mb-4">
                            Hello, Manager!
                        </h2>
                        <p className="text-lg leading-relaxed opacity-90">
                            I will be your helpful copilot in the world of
                            Tango. This uses the exact color scheme from your
                            design system with dark mode support.
                        </p>
                    </div>

                    {/* Secondary card */}
                    <div className="bg-secondary-light dark:bg-secondary-dark border border-border-secondary-light dark:border-border-secondary-dark rounded-2xl p-6 shadow-medium mt-6 hover:bg-secondary-hovered-light dark:hover:bg-secondary-hovered-dark transition-colors">
                        <h3 className="text-xl font-semibold text-foreground-heading-light dark:text-foreground-heading-dark mb-3">
                            Secondary Style
                        </h3>
                        <p className="text-foreground-body-light dark:text-foreground-body-dark mb-4">
                            This card uses secondary colors from your exact
                            design system with dark mode variants.
                        </p>
                        <button className="bg-secondary-light dark:bg-secondary-dark border border-border-secondary-light dark:border-border-secondary-dark text-foreground-secondary-light dark:text-foreground-secondary-dark px-4 py-2 rounded-xl hover:bg-secondary-hovered-light dark:hover:bg-secondary-hovered-dark hover:border-border-secondary-hovered-light dark:hover:border-border-secondary-hovered-dark transition-all">
                            Secondary Button
                        </button>
                    </div>

                    {/* Tag examples using exact colors */}
                    <div className="mt-8">
                        <h3 className="text-xl font-semibold text-foreground-heading-light dark:text-foreground-heading-dark mb-4">
                            Tag Colors (Light & Dark Mode)
                        </h3>
                        <div className="flex flex-wrap gap-3">
                            <span className="bg-tag-red-light border border-tag-red-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-red-hovered-light transition-colors">
                                Red Tag
                            </span>
                            <span className="bg-tag-orange-light border border-tag-orange-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-orange-hovered-light transition-colors">
                                Orange Tag
                            </span>
                            <span className="bg-tag-yellow-light border border-tag-yellow-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-yellow-hovered-light transition-colors">
                                Yellow Tag
                            </span>
                            <span className="bg-tag-green-light border border-tag-green-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-green-hovered-light transition-colors">
                                Green Tag
                            </span>
                            <span className="bg-tag-blue-light border border-tag-blue-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-blue-hovered-light transition-colors">
                                Blue Tag
                            </span>
                            <span className="bg-tag-purple-light border border-tag-purple-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-purple-hovered-light transition-colors">
                                Purple Tag
                            </span>
                            <span className="bg-tag-pink-light border border-tag-pink-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-pink-hovered-light transition-colors">
                                Pink Tag
                            </span>
                            <span className="bg-tag-grey-light border border-tag-grey-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-grey-hovered-light transition-colors">
                                Grey Tag
                            </span>
                        </div>
                    </div>

                    {/* Form field example */}
                    <div className="mt-8 max-w-md">
                        <label className="block text-foreground-fieldLabel-light dark:text-foreground-fieldLabel-dark font-medium mb-2">
                            Example Field (Dark Mode Aware)
                        </label>
                        <input
                            type="text"
                            placeholder="Type something..."
                            className="w-full bg-field-light dark:bg-field-dark border border-border-field-light dark:border-border-field-dark text-foreground-field-light dark:text-foreground-field-dark px-4 py-3 rounded-xl hover:bg-field-hovered-light dark:hover:bg-field-hovered-dark hover:border-border-field-hovered-light dark:hover:border-border-field-hovered-dark focus:bg-field-focused-light dark:focus:bg-field-focused-dark focus:border-border-field-focused-light dark:focus:border-border-field-focused-dark focus:outline-none transition-all placeholder:text-foreground-body-light dark:placeholder:text-foreground-body-dark"
                        />
                    </div>

                    {/* Destructive button example */}
                    <div className="mt-6">
                        <button className="bg-destructive-light dark:bg-destructive-dark border border-border-destructive-light dark:border-border-destructive-dark text-foreground-destructive-light dark:text-foreground-destructive-dark px-6 py-3 rounded-xl hover:bg-destructive-hovered-light dark:hover:bg-destructive-hovered-dark hover:border-border-destructive-hovered-light dark:hover:border-border-destructive-hovered-dark transition-all font-medium">
                            Destructive Action
                        </button>
                    </div>

                    {/* Dark mode info */}
                    <div className="mt-8 p-4 bg-tag-blue-light dark:bg-tag-blue-dark border border-tag-blue-light dark:border-tag-blue-dark rounded-xl">
                        <p className="text-foreground-tag-light dark:text-foreground-tag-dark text-sm">
                            💡 <strong>Dark Mode:</strong> Toggle the switch in
                            the top-right corner to see your exact color scheme
                            in both light and dark modes. The theme
                            automatically saves your preference and respects
                            system settings.
                        </p>
                    </div>
                </ErrorBoundary>
            </div>
        </div>
    );
};

export default Home;
