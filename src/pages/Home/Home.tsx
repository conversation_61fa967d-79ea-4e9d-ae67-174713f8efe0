import React from 'react';
import ErrorBoundary from '@components/ErrorBoundary';

const Home = () => {
    return (
        <div className="container p-4">
            <ErrorBoundary>
                {/* Using custom display font sizes and colors */}
                <h1 className="text-display-lg text-primary-600 mb-6 animate-fade-in">
                    Tango Copilot
                </h1>

                {/* Using custom colors, spacing, shadows, and border radius */}
                <div className="bg-primary-50 border border-primary-200 rounded-3xl p-6 shadow-soft mt-8">
                    <h2 className="text-display-sm text-secondary-800 mb-4">
                        Hello, Manager!
                    </h2>
                    <p className="text-lg text-secondary-600 leading-relaxed">
                        I will be your helpful copilot in the world of Tango.
                        This example showcases our custom theme elements.
                    </p>
                </div>

                {/* Example of custom theme usage */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                    <div className="bg-white rounded-2xl p-6 shadow-medium border border-secondary-200 hover:shadow-hard transition-shadow duration-300">
                        <h3 className="text-xl font-semibold text-primary-700 mb-3">
                            Custom Colors
                        </h3>
                        <p className="text-secondary-600 mb-4">
                            Primary, secondary, and accent color palettes with
                            50-900 shades.
                        </p>
                        <button className="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-xl transition-colors">
                            Try Primary
                        </button>
                    </div>

                    <div className="bg-accent-50 rounded-2xl p-6 shadow-medium border border-accent-200">
                        <h3 className="text-xl font-semibold text-accent-700 mb-3">
                            Custom Animations
                        </h3>
                        <p className="text-secondary-600 mb-4">
                            Custom spacing (18, 88, 128), shadows, and
                            animations.
                        </p>
                        <div className="w-12 h-12 bg-accent-500 rounded-xl animate-bounce-gentle"></div>
                    </div>
                </div>
            </ErrorBoundary>
        </div>
    );
};

export default Home;
