import React from 'react';
import ErrorBoundary from '@components/ErrorBoundary';

const Home = () => {
    return (
        <div className="bg-background-light min-h-screen">
            <div className="container p-4">
                <ErrorBoundary>
                    {/* Using exact design system colors */}
                    <h1 className="text-display-lg text-foreground-title-light mb-6 animate-fade-in">
                        Tango Copilot
                    </h1>

                    {/* Primary card using exact color scheme */}
                    <div className="bg-primary-light text-foreground-primary-light rounded-3xl p-6 shadow-soft mt-8 hover:bg-primary-hovered-light transition-colors">
                        <h2 className="text-display-sm mb-4">
                            Hello, Manager!
                        </h2>
                        <p className="text-lg leading-relaxed opacity-90">
                            I will be your helpful copilot in the world of
                            Tango. This uses the exact color scheme from your
                            design system.
                        </p>
                    </div>

                    {/* Secondary card */}
                    <div className="bg-secondary-light border border-border-secondary-light rounded-2xl p-6 shadow-medium mt-6 hover:bg-secondary-hovered-light transition-colors">
                        <h3 className="text-xl font-semibold text-foreground-heading-light mb-3">
                            Secondary Style
                        </h3>
                        <p className="text-foreground-body-light mb-4">
                            This card uses secondary colors from your exact
                            design system.
                        </p>
                        <button className="bg-secondary-light border border-border-secondary-light text-foreground-secondary-light px-4 py-2 rounded-xl hover:bg-secondary-hovered-light hover:border-border-secondary-hovered-light transition-all">
                            Secondary Button
                        </button>
                    </div>

                    {/* Tag examples using exact colors */}
                    <div className="mt-8">
                        <h3 className="text-xl font-semibold text-foreground-heading-light mb-4">
                            Tag Colors
                        </h3>
                        <div className="flex flex-wrap gap-3">
                            <span className="bg-tag-red-light border border-tag-red-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-red-hovered-light transition-colors">
                                Red Tag
                            </span>
                            <span className="bg-tag-orange-light border border-tag-orange-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-orange-hovered-light transition-colors">
                                Orange Tag
                            </span>
                            <span className="bg-tag-yellow-light border border-tag-yellow-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-yellow-hovered-light transition-colors">
                                Yellow Tag
                            </span>
                            <span className="bg-tag-green-light border border-tag-green-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-green-hovered-light transition-colors">
                                Green Tag
                            </span>
                            <span className="bg-tag-blue-light border border-tag-blue-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-blue-hovered-light transition-colors">
                                Blue Tag
                            </span>
                            <span className="bg-tag-purple-light border border-tag-purple-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-purple-hovered-light transition-colors">
                                Purple Tag
                            </span>
                            <span className="bg-tag-pink-light border border-tag-pink-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-pink-hovered-light transition-colors">
                                Pink Tag
                            </span>
                            <span className="bg-tag-grey-light border border-tag-grey-light text-foreground-tag-light px-3 py-1 rounded-lg text-sm hover:bg-tag-grey-hovered-light transition-colors">
                                Grey Tag
                            </span>
                        </div>
                    </div>

                    {/* Form field example */}
                    <div className="mt-8 max-w-md">
                        <label className="block text-foreground-fieldLabel-light font-medium mb-2">
                            Example Field
                        </label>
                        <input
                            type="text"
                            placeholder="Type something..."
                            className="w-full bg-field-light border border-border-field-light text-foreground-field-light px-4 py-3 rounded-xl hover:bg-field-hovered-light hover:border-border-field-hovered-light focus:bg-field-focused-light focus:border-border-field-focused-light focus:outline-none transition-all"
                        />
                    </div>

                    {/* Destructive button example */}
                    <div className="mt-6">
                        <button className="bg-destructive-light border border-border-destructive-light text-foreground-destructive-light px-6 py-3 rounded-xl hover:bg-destructive-hovered-light hover:border-border-destructive-hovered-light transition-all font-medium">
                            Destructive Action
                        </button>
                    </div>
                </ErrorBoundary>
            </div>
        </div>
    );
};

export default Home;
