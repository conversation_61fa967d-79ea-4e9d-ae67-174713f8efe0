// Theme

/**
 * BACKGROUNDS
 */

const backgroundLight = 0xffffffff;
const backgroundDark = 0xff252045;

const modalLight = 0xffffffff;
const modalDark = 0xffffffff;

const cardHoveredLight = 0xfff8f8f8;
const cardHoveredDark = 0xfff8f8f8;

const cardPressedLight = 0xfff1f1f1;
const cardPressedDark = 0xfff1f1f1;

// Primary
const primaryLight = 0xff533cf3;
const primaryDark = 0xffffffff;

const primaryHoveredLight = 0xff4533cc;
const primaryHoveredDark = 0xfff0f0ff;

const primaryPressedLight = 0xff372ba6;
const primaryPressedDark = 0xffe0e0ff;

const primaryDisabledLight = 0xfff8f8f8;
const primaryDisabledDark = 0xfff8f8f8;

// Secondary
const secondaryLight = 0xffffffff;
const secondaryDark = 0x00ffffff;

const secondaryHoveredLight = 0xfffbfbfb;
const secondaryHoveredDark = 0xff2e274d;

const secondaryPressedLight = 0xfff8f8f8;
const secondaryPressedDark = 0xff3a3061;

const secondaryDisabledLight = 0xfff8f8f8;
const secondaryDisabledDark = 0xfff8f8f8;

// Destructive
const destructiveLight = 0xffffffff;
const destructiveDark = 0xffffffff;

const destructiveHoveredLight = 0x80fae4df;
const destructiveHoveredDark = 0xfffae4df;

const destructivePressedLight = 0xfffae4df;
const destructivePressedDark = 0xfffae4df;

const destructiveDisabledLight = 0xfff8f8f8;
const destructiveDisabledDark = 0xfff8f8f8;

// Field

const fieldLight = 0xffffffff;
const fieldDark = 0xffffffff;

const fieldHoveredLight = 0xfffbfbfb;
const fieldHoveredDark = 0xfffbfbfb;

const fieldFocusedLight = 0xfff1f1f1;
const fieldFocusedDark = 0xfff1f1f1;

const fieldDisabledLight = 0xfff8f8f8;
const fieldDisabledDark = 0xfff8f8f8;

const fieldErrorLight = 0xffffffff;
const fieldErrorDark = 0xffffffff;

// Tags
const tagRedLight = 0xfffae4df;
const tagRedDark = 0xfffae4df;

const tagRedHoveredLight = 0xfff9d8d4;
const tagRedHoveredDark = 0xfff9d8d4;

const tagRedPressedLight = 0xfff6c4be;
const tagRedPressedDark = 0xfff6c4be;

const tagOrangeLight = 0xfffaeddf;
const tagOrangeDark = 0xfffaeddf;

const tagOrangeHoveredLight = 0xfff9e5d4;
const tagOrangeHoveredDark = 0xfff9e5d4;

const tagOrangePressedLight = 0xfff6d7be;
const tagOrangePressedDark = 0xfff6d7be;

const tagYellowLight = 0xfffaf7e0;
const tagYellowDark = 0xfffaf7e0;

const tagYellowHoveredLight = 0xfff8f2d4;
const tagYellowHoveredDark = 0xfff8f2d4;

const tagYellowPressedLight = 0xfff5ebbe;
const tagYellowPressedDark = 0xfff5ebbe;

const tagGreenLight = 0xfff2f9ee;
const tagGreenDark = 0xfff2f9ee;

const tagGreenHoveredLight = 0xffdef1de;
const tagGreenHoveredDark = 0xffdef1de;

const tagGreenPressedLight = 0xffcdeacc;
const tagGreenPressedDark = 0xffcdeacc;

const tagBlueLight = 0xffecf5f8;
const tagBlueDark = 0xffecf5f8;

const tagBlueHoveredLight = 0xffe0eef4;
const tagBlueHoveredDark = 0xffe0eef4;

const tagBluePressedLight = 0xffcfe6ee;
const tagBluePressedDark = 0xffcfe6ee;

const tagPurpleLight = 0xfff4f4fe;
const tagPurpleDark = 0xfff4f4fe;

const tagPurpleHoveredLight = 0xffe3e5f7;
const tagPurpleHoveredDark = 0xffe3e5f7;

const tagPurplePressedLight = 0xffe3e5f7;
const tagPurplePressedDark = 0xffe3e5f7;

const tagPinkLight = 0xfffdf2f9;
const tagPinkDark = 0xfffdf2f9;

const tagPinkHoveredLight = 0xfff8e5f0;
const tagPinkHoveredDark = 0xfff8e5f0;

const tagPinkPressedLight = 0xfff8e5f0;
const tagPinkPressedDark = 0xfff8e5f0;

const tagGreyLight = 0xfff8f8f8;
const tagGreyDark = 0xfff8f8f8;

const tagGreyHoveredLight = 0xfff1f1f1;
const tagGreyHoveredDark = 0xfff1f1f1;

const tagGreyPressedLight = 0xffdddddd;
const tagGreyPressedDark = 0xffdddddd;

/**
 * BORDERS
 */

const borderLight = 0xffdddddd;
const borderDark = 0xffdddddd;

const modalBorderLight = 0xffdddddd;
const modalBorderDark = 0xffdddddd;

// Primary
const primaryBorderLight = 0x00ffffff;
const primaryBorderDark = 0x00ffffff;

const primaryBorderHoveredLight = 0x00ffffff;
const primaryBorderHoveredDark = 0x00ffffff;

const primaryBorderPressedLight = 0x00ffffff;
const primaryBorderPressedDark = 0x00ffffff;

const primaryBorderDisabledLight = 0xffdddddd;
const primaryBorderDisabledDark = 0xffdddddd;

// Secondary
const secondaryBorderLight = 0xffdddddd;
const secondaryBorderDark = 0xffffffff;

const secondaryBorderHoveredLight = 0xfffbfbfb;
const secondaryBorderHoveredDark = 0xffdddddd;

const secondaryBorderPressedLight = 0xffc1c1c1;
const secondaryBorderPressedDark = 0xffc1c1c1;

const secondaryBorderDisabledLight = 0xfff8f8f8;
const secondaryBorderDisabledDark = 0xfff8f8f8;

// Destructive
const destructiveBorderLight = 0xffdddddd;
const destructiveBorderDark = 0x00ffffff;

const destructiveBorderHoveredLight = 0xfff9d8d4;
const destructiveBorderHoveredDark = 0x00ffffff;

const destructiveBorderPressedLight = 0xfff6c4be;
const destructiveBorderPressedDark = 0x00ffffff;

const destructiveBorderDisabledLight = 0xffdddddd;
const destructiveBorderDisabledDark = 0xfffdddddd;

// Field

const fieldBorderLight = 0xffdddddd;
const fieldBorderDark = 0xffdddddd;

const fieldBorderHoveredLight = 0xffdddddd;
const fieldBorderHoveredDark = 0xffdddddd;

const fieldBorderFocusedLight = 0xffe0e0ff;
const fieldBorderFocusedDark = 0xffe0e0ff;

const fieldBorderDisabledLight = 0xffdddddd;
const fieldBorderDisabledDark = 0xffdddddd;

const fieldBorderErrorLight = 0xfff17c75;
const fieldBorderErrorDark = 0xfff17c75;

// Tags

const tagRedBorderLight = 0xfff9d8d4;
const tagRedBorderDark = 0xfff9d8d4;

const tagRedBorderHoveredLight = 0xfff6c4be;
const tagRedBorderHoveredDark = 0xfff6c4be;

const tagRedBorderPressedLight = 0xffee837c;
const tagRedBorderPressedDark = 0xffee837c;

const tagOrangeBorderLight = 0xfff9e5d4;
const tagOrangeBorderDark = 0xfff9e5d4;

const tagOrangeBorderHoveredLight = 0xfff6d7be;
const tagOrangeBorderHoveredDark = 0xfff6d7be;

const tagOrangeBorderPressedLight = 0xffeeac7c;
const tagOrangeBorderPressedDark = 0xffeeac7c;

const tagYellowBorderLight = 0xfff8f2d4;
const tagYellowBorderDark = 0xfff8f2d4;

const tagYellowBorderHoveredLight = 0xfff5ebbe;
const tagYellowBorderHoveredDark = 0xfff5ebbe;

const tagYellowBorderPressedLight = 0xffecd37b;
const tagYellowBorderPressedDark = 0xffecd37b;

const tagGreenBorderLight = 0xffdef1de;
const tagGreenBorderDark = 0xffdef1de;

const tagGreenBorderHoveredLight = 0xffcdeacc;
const tagGreenBorderHoveredDark = 0xffcdeacc;

const tagGreenBorderPressedLight = 0xff7fc686;
const tagGreenBorderPressedDark = 0xff7fc686;

const tagBlueBorderLight = 0xffe0eef4;
const tagBlueBorderDark = 0xffe0eef4;

const tagBlueBorderHoveredLight = 0xffe0eef4;
const tagBlueBorderHoveredDark = 0xffe0eef4;

const tagBlueBorderPressedLight = 0xff95c8d9;
const tagBlueBorderPressedDark = 0xff95c8d9;

const tagPurpleBorderLight = 0xffe3e5f7;
const tagPurpleBorderDark = 0xffe3e5f7;

const tagPurpleBorderHoveredLight = 0xffd5d7f3;
const tagPurpleBorderHoveredDark = 0xffd5d7f3;

const tagPurpleBorderPressedLight = 0xff979edd;
const tagPurpleBorderPressedDark = 0xff979edd;

const tagPinkBorderLight = 0xfff8e5f0;
const tagPinkBorderDark = 0xfff8e5f0;

const tagPinkBorderHoveredLight = 0xfff5d7e9;
const tagPinkBorderHoveredDark = 0xfff5d7e9;

const tagPinkBorderPressedLight = 0xffe5a1ca;
const tagPinkBorderPressedDark = 0xffe5a1ca;

const tagGreyBorderLight = 0xffdddddd;
const tagGreyBorderDark = 0xffdddddd;

const tagGreyBorderHoveredLight = 0xffdddddd;
const tagGreyBorderHoveredDark = 0xffdddddd;

const tagGreyBorderPressedLight = 0xffc1c1c1;
const tagGreyBorderPressedDark = 0xffc1c1c1;

/**
 * FOREGROUND
 */

const titleForegroundLight = 0xff1a1f36;
const titleForegroundDark = 0xff1a1f36; // TODO: Make correct

const sectionForegroundLight = 0xff272727;
const sectionForegroundDark = 0xff272727;

const headingForegroundLight = 0xff272727;
const headingForegroundDark = 0xff272727;

const bodyForegroundLight = 0xff666666;
const bodyForegroundDark = 0xff666666;

const attentionForegroundLight = 0xff533cf3;
const attentionForegroundDark = 0xff533cf3;

const fieldLabelForegroundLight = 0xff272727;
const fieldLabelForegroundDark = 0xff272727;

const starredForegroundLight = 0xffefbf04;
const starredForegroundDark = 0xffefbf04;

// Primary
const primaryForegroundLight = 0xffffffff;
const primaryForegroundDark = 0xff533cf3;

const primaryForegroundHoveredLight = 0xffffffff;
const primaryForegroundHoveredDark = 0xff533cf3;

const primaryForegroundPressedLight = 0xffffffff;
const primaryForegroundPressedDark = 0xff4839d1;

const primaryForegroundDisabledLight = 0xffc1c1c1;
const primaryForegroundDisabledDark = 0xffc1c1c1;

// Secondary
const secondaryForegroundLight = 0xff272727;
const secondaryForegroundDark = 0xffffffff;

const secondaryForegroundHoveredLight = 0xff272727;
const secondaryForegroundHoveredDark = 0xffffffff;

const secondaryForegroundPressedLight = 0xff272727;
const secondaryForegroundPressedDark = 0xffffffff;

const secondaryForegroundDisabledLight = 0xffc1c1c1;
const secondaryForegroundDisabledDark = 0xffc1c1c1;

// Destructive
const destructiveForegroundLight = 0xffe3564d;
const destructiveForegroundDark = 0xffe3564d;

const destructiveForegroundHoveredLight = 0xffe3564d;
const destructiveForegroundHoveredDark = 0xffe3564d;

const destructiveForegroundPressedLight = 0xffe3564d;
const destructiveForegroundPressedDark = 0xffe3564d;

const destructiveForegroundDisabledLight = 0xffc1c1c1;
const destructiveForegroundDisabledDark = 0xffc1c1c1;

// Icon
const iconForegroundLight = 0xffc1c1c1;
const iconForegroundDark = 0xffc1c1c1;

const iconForegroundHoveredLight = 0xff888888;
const iconForegroundHoveredDark = 0xff888888;

const iconForegroundPressedLight = 0xff533cf3;
const iconForegroundPressedDark = 0xff533cf3;

const iconForegroundDisabledLight = 0xffdddddd;
const iconForegroundDisabledDark = 0xffdddddd;

// Field

const fieldForegroundLight = 0xff272727;
const fieldForegroundDark = 0xff272727;

const fieldForegroundHoveredLight = 0xff272727;
const fieldForegroundHoveredDark = 0xff272727;

const fieldForegroundFocusedLight = 0xff272727;
const fieldForegroundFocusedDark = 0xff272727;

const fieldForegroundDisabledLight = 0xffc1c1c1;
const fieldForegroundDisabledDark = 0xffc1c1c1;

const fieldForegroundErrorLight = 0xff272727;
const fieldForegroundErrorDark = 0xff272727;

// Tags
const tagForegroundLight = 0xff272727;
const tagForegroundDark = 0xff272727;

// Selection
const selectionLight = 0xff533cf3;
const selectionDark = 0xff533cf3;
