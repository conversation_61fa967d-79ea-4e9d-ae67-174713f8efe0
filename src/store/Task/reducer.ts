import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type TaskType = 'inventory' | 'reporting' | 'scheduling' | 'menu';

export type Task = {
    id: string;
    question: string;
    response: string;
    taskType: TaskType;
    completed: boolean;
};

export type TaskState = {
    /**
     * tasks data
     */
    tasks: Task[];
};

export const initialTaskState: TaskState = {
    tasks: [],
};

const slice = createSlice({
    name: 'Task',
    initialState: initialTaskState,
    reducers: {
        taskAdded: (state, action: PayloadAction<Task>) => {
            state.tasks.push(action.payload);
        },
    },
});

const { reducer } = slice;

export const { taskAdded } = slice.actions;

export default reducer;
