{"name": "react-redux-boilerplate", "version": "1.0.0", "description": "Start React+Redux project with TypeScript in an easier and faster way", "main": "index.js", "scripts": {"test": "jest --passWithNoTests", "start": "webpack serve --mode development --open", "dev": "webpack --mode development", "build": "webpack --mode production", "prepare": "husky install"}, "lint-staged": {"src/**/*.{ts,tsx,json}": ["prettier --write", "eslint src --fix", "jest --bail --findRelatedTests --passWithNoTests"]}, "repository": {"type": "git", "url": "git+https://github.com/vannizhang/react-redux-boilerplate.git"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/vannizhang/react-redux-boilerplate/issues"}, "homepage": "https://github.com/vannizhang/react-redux-boilerplate", "devDependencies": {"@babel/core": "7.25", "@babel/plugin-transform-runtime": "^7.11.5", "@babel/preset-env": "^7.25.3", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@babel/runtime": "^7.25.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/classnames": "^2.2.10", "@types/jest": "^30.0.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-redux": "^7.1.34", "@types/react-responsive": "^8.0.2", "@types/redux-mock-store": "^1.0.3", "@typescript-eslint/eslint-plugin": "6.12", "@typescript-eslint/parser": "6.12", "autoprefixer": "^10.2.6", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^12.0.2", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.0", "eslint": "8.54", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "fork-ts-checker-webpack-plugin": "^9.0.2", "html-loader": "^5.1.0", "html-webpack-plugin": "^5.6.0", "husky": "^8.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^13.0.3", "mini-css-extract-plugin": "^2.9.0", "postcss": "8.4", "postcss-loader": "7.3", "postcss-preset-env": "8.4", "prettier": "^2.7.1", "source-map-loader": "^3.0.0", "style-loader": "^4.0.0", "tailwindcss": "3.3", "terser-webpack-plugin": "^5.3.10", "typescript": "^5.5.4", "webpack": "^5.93.0", "webpack-bundle-analyzer": "^4.4.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4"}, "dependencies": {"@reduxjs/toolkit": "2.5", "@testing-library/dom": "^10.4.0", "classnames": "^2.2.6", "helper-toolkit-ts": "^1.1.13", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "redux": "^5.0.1"}, "browserslist": ["defaults"]}