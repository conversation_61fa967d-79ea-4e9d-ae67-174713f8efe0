module.exports = {
    content: ['./src/**/*.html', './src/**/*.{js,jsx,ts,tsx}'],
    darkMode: 'class', // Enable class-based dark mode
    theme: {
        extend: {
            fontFamily: {
                sans: ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif'],
            },
            colors: {
                // Exact color scheme from your design system

                // Backgrounds
                background: {
                    light: '#ffffff',
                    dark: '#252045',
                },
                modal: {
                    light: '#ffffff',
                    dark: '#ffffff',
                },
                card: {
                    hovered: {
                        light: '#f8f8f8',
                        dark: '#f8f8f8',
                    },
                    pressed: {
                        light: '#f1f1f1',
                        dark: '#f1f1f1',
                    },
                },

                // Primary colors
                primary: {
                    light: '#533cf3',
                    dark: '#ffffff',
                    hovered: {
                        light: '#4533cc',
                        dark: '#f0f0ff',
                    },
                    pressed: {
                        light: '#372ba6',
                        dark: '#e0e0ff',
                    },
                    disabled: {
                        light: '#f8f8f8',
                        dark: '#f8f8f8',
                    },
                },

                // Secondary colors
                secondary: {
                    light: '#ffffff',
                    dark: 'transparent',
                    hovered: {
                        light: '#fbfbfb',
                        dark: '#2e274d',
                    },
                    pressed: {
                        light: '#f8f8f8',
                        dark: '#3a3061',
                    },
                    disabled: {
                        light: '#f8f8f8',
                        dark: '#f8f8f8',
                    },
                },

                // Destructive colors
                destructive: {
                    light: '#ffffff',
                    dark: '#ffffff',
                    hovered: {
                        light: '#fae4df80',
                        dark: '#fae4df',
                    },
                    pressed: {
                        light: '#fae4df',
                        dark: '#fae4df',
                    },
                    disabled: {
                        light: '#f8f8f8',
                        dark: '#f8f8f8',
                    },
                },

                // Field colors
                field: {
                    light: '#ffffff',
                    dark: '#ffffff',
                    hovered: {
                        light: '#fbfbfb',
                        dark: '#fbfbfb',
                    },
                    focused: {
                        light: '#f1f1f1',
                        dark: '#f1f1f1',
                    },
                    disabled: {
                        light: '#f8f8f8',
                        dark: '#f8f8f8',
                    },
                    error: {
                        light: '#ffffff',
                        dark: '#ffffff',
                    },
                },

                // Tag colors
                tag: {
                    red: {
                        light: '#fae4df',
                        dark: '#fae4df',
                        hovered: {
                            light: '#f9d8d4',
                            dark: '#f9d8d4',
                        },
                        pressed: {
                            light: '#f6c4be',
                            dark: '#f6c4be',
                        },
                    },
                    orange: {
                        light: '#faeddf',
                        dark: '#faeddf',
                        hovered: {
                            light: '#f9e5d4',
                            dark: '#f9e5d4',
                        },
                        pressed: {
                            light: '#f6d7be',
                            dark: '#f6d7be',
                        },
                    },
                    yellow: {
                        light: '#faf7e0',
                        dark: '#faf7e0',
                        hovered: {
                            light: '#f8f2d4',
                            dark: '#f8f2d4',
                        },
                        pressed: {
                            light: '#f5ebbe',
                            dark: '#f5ebbe',
                        },
                    },
                    green: {
                        light: '#f2f9ee',
                        dark: '#f2f9ee',
                        hovered: {
                            light: '#def1de',
                            dark: '#def1de',
                        },
                        pressed: {
                            light: '#cdeacc',
                            dark: '#cdeacc',
                        },
                    },
                    blue: {
                        light: '#ecf5f8',
                        dark: '#ecf5f8',
                        hovered: {
                            light: '#e0eef4',
                            dark: '#e0eef4',
                        },
                        pressed: {
                            light: '#cfe6ee',
                            dark: '#cfe6ee',
                        },
                    },
                    purple: {
                        light: '#f4f4fe',
                        dark: '#f4f4fe',
                        hovered: {
                            light: '#e3e5f7',
                            dark: '#e3e5f7',
                        },
                        pressed: {
                            light: '#e3e5f7',
                            dark: '#e3e5f7',
                        },
                    },
                    pink: {
                        light: '#fdf2f9',
                        dark: '#fdf2f9',
                        hovered: {
                            light: '#f8e5f0',
                            dark: '#f8e5f0',
                        },
                        pressed: {
                            light: '#f8e5f0',
                            dark: '#f8e5f0',
                        },
                    },
                    grey: {
                        light: '#f8f8f8',
                        dark: '#f8f8f8',
                        hovered: {
                            light: '#f1f1f1',
                            dark: '#f1f1f1',
                        },
                        pressed: {
                            light: '#dddddd',
                            dark: '#dddddd',
                        },
                    },
                },

                // Border colors
                border: {
                    light: '#dddddd',
                    dark: '#dddddd',
                    modal: {
                        light: '#dddddd',
                        dark: '#dddddd',
                    },
                    primary: {
                        light: 'transparent',
                        dark: 'transparent',
                        hovered: {
                            light: 'transparent',
                            dark: 'transparent',
                        },
                        pressed: {
                            light: 'transparent',
                            dark: 'transparent',
                        },
                        disabled: {
                            light: '#dddddd',
                            dark: '#dddddd',
                        },
                    },
                    secondary: {
                        light: '#dddddd',
                        dark: '#ffffff',
                        hovered: {
                            light: '#fbfbfb',
                            dark: '#dddddd',
                        },
                        pressed: {
                            light: '#c1c1c1',
                            dark: '#c1c1c1',
                        },
                        disabled: {
                            light: '#f8f8f8',
                            dark: '#f8f8f8',
                        },
                    },
                    destructive: {
                        light: '#dddddd',
                        dark: 'transparent',
                        hovered: {
                            light: '#f9d8d4',
                            dark: 'transparent',
                        },
                        pressed: {
                            light: '#f6c4be',
                            dark: 'transparent',
                        },
                        disabled: {
                            light: '#dddddd',
                            dark: '#dddddd',
                        },
                    },
                    field: {
                        light: '#dddddd',
                        dark: '#dddddd',
                        hovered: {
                            light: '#dddddd',
                            dark: '#dddddd',
                        },
                        focused: {
                            light: '#e0e0ff',
                            dark: '#e0e0ff',
                        },
                        disabled: {
                            light: '#dddddd',
                            dark: '#dddddd',
                        },
                        error: {
                            light: '#f17c75',
                            dark: '#f17c75',
                        },
                    },
                },

                // Foreground/Text colors
                foreground: {
                    title: {
                        light: '#1a1f36',
                        dark: '#1a1f36',
                    },
                    section: {
                        light: '#272727',
                        dark: '#272727',
                    },
                    heading: {
                        light: '#272727',
                        dark: '#272727',
                    },
                    body: {
                        light: '#666666',
                        dark: '#666666',
                    },
                    attention: {
                        light: '#533cf3',
                        dark: '#533cf3',
                    },
                    fieldLabel: {
                        light: '#272727',
                        dark: '#272727',
                    },
                    starred: {
                        light: '#efbf04',
                        dark: '#efbf04',
                    },
                    primary: {
                        light: '#ffffff',
                        dark: '#533cf3',
                        hovered: {
                            light: '#ffffff',
                            dark: '#533cf3',
                        },
                        pressed: {
                            light: '#ffffff',
                            dark: '#4839d1',
                        },
                        disabled: {
                            light: '#c1c1c1',
                            dark: '#c1c1c1',
                        },
                    },
                    secondary: {
                        light: '#272727',
                        dark: '#ffffff',
                        hovered: {
                            light: '#272727',
                            dark: '#ffffff',
                        },
                        pressed: {
                            light: '#272727',
                            dark: '#ffffff',
                        },
                        disabled: {
                            light: '#c1c1c1',
                            dark: '#c1c1c1',
                        },
                    },
                    destructive: {
                        light: '#e3564d',
                        dark: '#e3564d',
                        hovered: {
                            light: '#e3564d',
                            dark: '#e3564d',
                        },
                        pressed: {
                            light: '#e3564d',
                            dark: '#e3564d',
                        },
                        disabled: {
                            light: '#c1c1c1',
                            dark: '#c1c1c1',
                        },
                    },
                    icon: {
                        light: '#c1c1c1',
                        dark: '#c1c1c1',
                        hovered: {
                            light: '#888888',
                            dark: '#888888',
                        },
                        pressed: {
                            light: '#533cf3',
                            dark: '#533cf3',
                        },
                        disabled: {
                            light: '#dddddd',
                            dark: '#dddddd',
                        },
                    },
                    field: {
                        light: '#272727',
                        dark: '#272727',
                        hovered: {
                            light: '#272727',
                            dark: '#272727',
                        },
                        focused: {
                            light: '#272727',
                            dark: '#272727',
                        },
                        disabled: {
                            light: '#c1c1c1',
                            dark: '#c1c1c1',
                        },
                        error: {
                            light: '#272727',
                            dark: '#272727',
                        },
                    },
                    tag: {
                        light: '#272727',
                        dark: '#272727',
                    },
                },

                // Selection
                selection: {
                    light: '#533cf3',
                    dark: '#533cf3',
                },
            },
            spacing: {
                // Custom spacing values
                18: '4.5rem',
                88: '22rem',
                128: '32rem',
            },
            fontSize: {
                // Custom font sizes with line heights
                xs: ['0.75rem', { lineHeight: '1rem' }],
                sm: ['0.875rem', { lineHeight: '1.25rem' }],
                base: ['1rem', { lineHeight: '1.5rem' }],
                lg: ['1.125rem', { lineHeight: '1.75rem' }],
                xl: ['1.25rem', { lineHeight: '1.75rem' }],
                '2xl': ['1.5rem', { lineHeight: '2rem' }],
                '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
                '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
                '5xl': ['3rem', { lineHeight: '1' }],
                '6xl': ['3.75rem', { lineHeight: '1' }],
                // Custom display sizes
                'display-sm': [
                    '2.5rem',
                    { lineHeight: '3rem', fontWeight: '600' },
                ],
                'display-md': [
                    '3.5rem',
                    { lineHeight: '4rem', fontWeight: '600' },
                ],
                'display-lg': [
                    '4.5rem',
                    { lineHeight: '5rem', fontWeight: '700' },
                ],
            },
            borderRadius: {
                // Custom border radius values
                xl: '0.75rem',
                '2xl': '1rem',
                '3xl': '1.5rem',
            },
            boxShadow: {
                // Custom shadows
                soft: '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
                medium: '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                hard: '0 10px 40px -10px rgba(0, 0, 0, 0.2)',
            },
            animation: {
                // Custom animations
                'fade-in': 'fadeIn 0.5s ease-in-out',
                'slide-up': 'slideUp 0.3s ease-out',
                'bounce-gentle': 'bounceGentle 2s infinite',
            },
            keyframes: {
                fadeIn: {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' },
                },
                slideUp: {
                    '0%': { transform: 'translateY(10px)', opacity: '0' },
                    '100%': { transform: 'translateY(0)', opacity: '1' },
                },
                bounceGentle: {
                    '0%, 100%': { transform: 'translateY(-5%)' },
                    '50%': { transform: 'translateY(0)' },
                },
            },
        },
    },
    variants: {
        extend: {},
    },
    plugins: [],
};
